# Face Shape Detector AI

An Astro.js website with React integration that uses Google's Gemini AI to analyze facial features and determine face shape from uploaded images.

## 🚀 Project Structure

```
/
├── public/
├── src/
│   ├── components/
│   │   ├── App.tsx          # Main React application component
│   │   ├── FileUpload.tsx   # File upload component with drag & drop
│   │   ├── ResultDisplay.tsx # Results display component
│   │   └── Icons.tsx        # Icon components
│   ├── layouts/
│   │   └── Layout.astro     # Base layout component
│   ├── lib/
│   │   ├── services/
│   │   │   └── geminiService.ts # Google Gemini AI integration
│   │   └── types.ts         # TypeScript type definitions
│   ├── pages/
│   │   └── index.astro      # Main page
│   └── styles/
│       └── global.css       # Global styles with Tailwind CSS
├── astro.config.mjs         # Astro configuration
├── tailwind.config.mjs      # Tailwind CSS configuration
└── package.json
```

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`     |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `npm run astro -- --help` | Get help using the Astro CLI                     |

## 🔧 Technology Stack

- **Astro.js** - Static site generator with React integration
- **React** - UI components with client-side interactivity
- **TypeScript** - Type safety and better development experience
- **Tailwind CSS** - Utility-first CSS framework
- **Google Gemini AI** - AI-powered face shape analysis
- **Vite** - Fast build tool and development server

## 🌟 Features

- **Drag & Drop Upload**: Easy image upload with visual feedback
- **AI Analysis**: Powered by Google's Gemini 2.5 Flash model
- **Real-time Results**: Instant face shape detection with confidence scores
- **Responsive Design**: Works on desktop and mobile devices
- **Modern UI**: Clean, gradient-based design with smooth animations

## 🚀 Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. Open your browser to `http://localhost:4321`

## 📝 Notes

This project was converted from a React SPA to an Astro.js website while maintaining all React functionality through Astro's React integration. The main benefits include:

- **Better Performance**: Static generation with selective hydration
- **SEO Friendly**: Server-side rendering capabilities
- **Modern Architecture**: Component-based with TypeScript support
- **Flexible Deployment**: Can be deployed as static site or server-rendered
