---
import Header from '~/components/Header.astro';
import Footer from '~/components/Footer.astro';

import config from '~/config/config.json';
import '~/styles/global.css';

export interface Props {
  title?: string;
}

const { title } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{title || config.site.title}</title>

    <!-- favicon -->
    <link rel="shortcut icon" href={config.site.favicon} />

    <!-- theme meta -->
    <meta name="theme-name" content="detect-faceshape" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#fff" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#000" />
    <meta name="generator" content={Astro.generator} />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Albert+Sans:ital@0;1&family=Alexandria:wght@700&family=Sora:wght@100..800&display=swap" rel="stylesheet" />

    <!-- canonical url -->
    <link rel="canonical" href={Astro.url.toString()} item-prop="url" />

    <!-- meta-description -->
    <meta name="description" content={config.metadata.meta_description} />

    <!-- author from config.json -->
    <meta name="author" content={config.metadata.meta_author} />

    <!-- og-title -->
    <meta property="og:title" content={config.site.title} />

    <!-- og-description -->
    <meta property="og:description" content={config.metadata.meta_description} />
    <meta property="og:type" content="website" />
    <meta property="og:url" content={`${config.site.base_url}/${Astro.url.pathname.replace('/', '')}`} />

    <!-- twitter-title -->
    <meta name="twitter:title" content={config.site.title} />

    <!-- twitter-description -->
    <meta name="twitter:description" content={config.metadata.meta_description} />

    <!-- og-image -->
    <meta property="og:image" content={`${config.site.base_url}${config.metadata.meta_image}`} />

    <!-- twitter-image -->
    <meta name="twitter:image" content={`${config.site.base_url}${config.metadata.meta_image}`} />
    <meta name="twitter:card" content="summary_large_image" />
  </head>
  <body class="font-text">
    <Header />
    <slot />
    <Footer />
  </body>
</html>
