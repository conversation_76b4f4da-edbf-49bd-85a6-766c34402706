---
import '../styles/global.css';

export interface Props {
  title: string;
}

const { title } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="Discover your face shape instantly with AI-powered analysis" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <title>{title}</title>
  </head>
  <body class="bg-slate-900 text-white">
    <slot />
  </body>
</html>

<style is:global>
  html {
    font-family: system-ui, sans-serif;
  }
  
  body {
    margin: 0;
    padding: 0;
  }
</style>
