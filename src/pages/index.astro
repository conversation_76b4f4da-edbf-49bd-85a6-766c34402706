---
import Layout from '~/layouts/Layout.astro';
import App from '~/components/App';

import WhyIcon1 from '~/images/why-icon-1.svg';
import WhyIcon2 from '~/images/why-icon-2.svg';
import WhyIcon3 from '~/images/why-icon-3.svg';
import WhyIcon4 from '~/images/why-icon-4.svg';
import WhyIcon5 from '~/images/why-icon-5.svg';
import WhyIcon6 from '~/images/why-icon-6.svg';
import PlusIcon from '~/images/plus.svg';
---

<Layout>
  <section class="bg-primary/[12%] pt-16 pb-11">
    <div class="container mx-auto flex flex-col items-center">
      <h1 class="text-[46px] font-bold font-primary">Precision Face Analysis in Seconds</h1>
      <p class="text-2xl mt-4">Instant AI-powered face shape detection for style and confidence.</p>
      <div>
        <App client:load />
      </div>
    </div>
  </section>
  <section id="how-it-works">
    <div class="container flex flex-col items-center mx-auto pt-20 pb-25">
      <h2>How It Works</h2>
      <p class="text-[22px] mt-5 text-center max-w-4xl">Upload your photo, let our AI analyze your features, and instantly see your accurate face shape — no app, no confusion, just precision.</p>
      <div class="mt-14 flex gap-8">
        <div class="flex flex-1 flex-col">
          <img class="rounded-4xl" src="/images/how-it-works-1.png" />
          <p class="mt-6 font-primary text-center font-bold text-3xl text-primary">1. Upload Your Photo</p>
          <p class="mt-3 text-xl text-center">Take or upload a clear photo of your face — no extra steps needed.</p>
        </div>
        <div class="flex flex-1 flex-col">
          <img class="rounded-4xl" src="/images/how-it-works-2.png" />
          <p class="mt-6 font-primary text-center font-bold text-3xl text-primary">2. AI Face Mapping</p>
          <p class="mt-3 text-xl text-center">Our advanced algorithm scans your facial contours with precision in seconds.</p>
        </div>
        <div class="flex flex-1 flex-col">
          <img class="rounded-4xl" src="/images/how-it-works-3.png" />
          <p class="mt-6 font-primary text-center font-bold text-3xl text-primary">3. Get Your Results</p>
          <p class="mt-3 text-xl text-center">Instantly discover your face shape and receive tailored insights for styling and grooming.</p>
        </div>
      </div>
    </div>
  </section>
  <section id="why-detect-faceshape" class="bg-primary/[12%]">
    <div class="container flex flex-col items-center mx-auto pt-20 pb-25">
      <h2>Why Detect FaceShape?</h2>
      <p class="text-[22px] mt-5 text-center max-w-4xl">Detect FaceShape isn't just another face scanner — it's built to deliver accurate, instant results with simplicity and trust in mind. Here's why users choose us:</p>
      <div class="mt-14 grid grid-cols-3 bg-primary gap-[1px]">
        <div class="flex flex-col bg-white pt-9 pb-18 px-8">
          <WhyIcon1 />
          <p class="mt-9 font-primary font-bold text-3xl">AI-Powered Precision</p>
          <p class="mt-3 text-xl">Detects your face shape with cutting-edge accuracy.</p>
        </div>
        <div class="flex flex-col bg-white pt-9 pb-18 px-8">
          <WhyIcon2 />
          <p class="mt-9 font-primary font-bold text-3xl">Fast & Effortless</p>
          <p class="mt-3 text-xl">Results delivered in just seconds.</p>
        </div>
        <div class="flex flex-col bg-white pt-9 pb-18 px-8">
          <WhyIcon3 />
          <p class="mt-9 font-primary font-bold text-3xl">Confidence Boost</p>
          <p class="mt-3 text-xl">Know your shape for better styling, grooming, and eyewear choices.</p>
        </div>
        <div class="flex flex-col bg-white pt-9 pb-18 px-8">
          <WhyIcon4 />
          <p class="mt-9 font-primary font-bold text-3xl">Privacy Guaranteed</p>
          <p class="mt-3 text-xl">Your photos are never stored or shared.</p>
        </div>
        <div class="flex flex-col bg-white pt-9 pb-18 px-8">
          <WhyIcon5 />
          <p class="mt-9 font-primary font-bold text-3xl">Accessible Anywhere</p>
          <p class="mt-3 text-xl">Works seamlessly on mobile, tablet, and desktop.</p>
        </div>
        <div class="flex flex-col bg-white pt-9 pb-18 px-8">
          <WhyIcon6 />
          <p class="mt-9 font-primary font-bold text-3xl">Modern & Minimal</p>
          <p class="mt-3 text-xl">A sleek, easy-to-use tool designed for everyone.</p>
        </div>
      </div>
    </div>
  </section>
  <section>
    <div class="container flex flex-col items-center mx-auto pt-20 pb-25">
      <h2>Types of Face Shapes</h2>
      <p class="text-[22px] mt-5 text-center max-w-4xl">Scale your business with expert solutions in design, development, and strategy — exactly when you need them.</p>
      <div class="mt-14 grid grid-cols-3 gap-2 text-white">
        <div class="flex flex-1 flex-col p-4 pb-8 rounded-3xl bg-primary/60">
          <img class="rounded-3xl" src="/images/types-of-face-shapes-1.png" />
          <p class="mt-6 font-primary font-bold text-3xl">1. Oval Face</p>
          <div class="pl-4">
            <p class="mt-4 font-primary font-bold text-2xl">Balanced & Versatile</p>
            <p class="mt-3 text-xl">Oval faces are slightly longer than wide, with softly rounded edges. Known as the most versatile shape, it complements a wide range of hairstyles and accessories.</p>
          </div>
        </div>
        <div class="flex flex-1 flex-col p-4 pb-8 rounded-3xl bg-primary/60">
          <img class="rounded-3xl" src="/images/types-of-face-shapes-2.png" />
          <p class="mt-6 font-primary font-bold text-3xl">2. Round Face</p>
          <div class="pl-4">
            <p class="mt-4 font-primary font-bold text-2xl">Soft & Youthful</p>
            <p class="mt-3 text-xl">Round faces have full cheeks and a softer jawline. This shape often gives a youthful and approachable look.</p>
          </div>
        </div>
        <div class="flex flex-1 flex-col p-4 pb-8 rounded-3xl bg-primary/60">
          <img class="rounded-3xl" src="/images/types-of-face-shapes-3.png" />
          <p class="mt-6 font-primary font-bold text-3xl">3. Square Face</p>
          <div class="pl-4">
            <p class="mt-4 font-primary font-bold text-2xl">Strong & Defined</p>
            <p class="mt-3 text-xl">Square faces are marked by a broad forehead and a strong jawline, giving a bold and confident appearance.</p>
          </div>
        </div>
        <div class="flex flex-1 flex-col p-4 pb-8 rounded-3xl bg-primary/60">
          <img class="rounded-3xl" src="/images/types-of-face-shapes-4.png" />
          <p class="mt-6 font-primary font-bold text-3xl">4. Heart Face</p>
          <div class="pl-4">
            <p class="mt-4 font-primary font-bold text-2xl">Charming & Distinctive</p>
            <p class="mt-3 text-xl">Heart-shaped faces feature a wider forehead that narrows toward the chin, creating a naturally attractive outline.</p>
          </div>
        </div>
        <div class="flex flex-1 flex-col p-4 pb-8 rounded-3xl bg-primary/60">
          <img class="rounded-3xl" src="/images/types-of-face-shapes-5.png" />
          <p class="mt-6 font-primary font-bold text-3xl">5. Diamond Face</p>
          <div class="pl-4">
            <p class="mt-4 font-primary font-bold text-2xl">Sharp & Elegant</p>
            <p class="mt-3 text-xl">Diamond faces are widest at the cheekbones with a narrow forehead and chin, giving a refined, angular look.</p>
          </div>
        </div>
        <div class="flex flex-1 flex-col p-4 pb-8 rounded-3xl bg-primary/60">
          <img class="rounded-3xl" src="/images/types-of-face-shapes-6.png" />
          <p class="mt-6 font-primary font-bold text-3xl">6. Oblong Face</p>
          <div class="pl-4">
            <p class="mt-4 font-primary font-bold text-2xl">Long & Refined</p>
            <p class="mt-3 text-xl">Oblong faces are longer than they are wide, with straight sides and a defined structure. This shape often carries a sophisticated feel.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section id="faq" class="bg-primary/[12%]">
    <div class="container flex flex-col items-center mx-auto pt-20 pb-25">
      <h2>Frequently Asked Questions</h2>
      <div class="mt-14 flex flex-col gap-5 w-full">
        <div class="flex flex-col rounded-3xl bg-white p-8">
          <div class="flex justify-between items-center cursor-pointer open-faq">
            <p class="font-primary font-bold text-2xl flex-1">How does Detect FaceShape detect my face shape?</p>
            <PlusIcon />
          </div>
          <div class="h-0 overflow-hidden transition-all duration-300">
            <p class="text-[22px] pt-4">Detect FaceShape uses advanced AI-powered analysis to scan key points on your face and determine the closest face shape with precision.</p>
          </div>
        </div>
        <div class="flex flex-col rounded-3xl bg-white p-8">
          <div class="flex justify-between items-center cursor-pointer open-faq">
            <p class="font-primary font-bold text-2xl flex-1">Is my photo stored or shared?</p>
            <PlusIcon />
          </div>
          <div class="h-0 overflow-hidden transition-all duration-300">
            <p class="text-[22px] pt-4">Your photo is not stored or shared. It is processed locally in your browser and discarded after analysis.</p>
          </div>
        </div>
        <div class="flex flex-col rounded-3xl bg-white p-8">
          <div class="flex justify-between items-center cursor-pointer open-faq">
            <p class="font-primary font-bold text-2xl flex-1">What face shapes can Detect FaceShape detect?</p>
            <PlusIcon />
          </div>
          <div class="h-0 overflow-hidden transition-all duration-300">
            <p class="text-[22px] pt-4">Detect FaceShape can detect the following face shapes: Oval, Round, Square, Heart, Diamond, and Oblong.</p>
          </div>
        </div>
        <div class="flex flex-col rounded-3xl bg-white p-8">
          <div class="flex justify-between items-center cursor-pointer open-faq">
            <p class="font-primary font-bold text-2xl flex-1">Do I need a clear photo for accurate results?</p>
            <PlusIcon />
          </div>
          <div class="h-0 overflow-hidden transition-all duration-300">
            <p class="text-[22px] pt-4">A clear photo is required for accurate results. A blurry or low-quality photo will not be processed.</p>
          </div>
        </div>
        <div class="flex flex-col rounded-3xl bg-white p-8">
          <div class="flex justify-between items-center cursor-pointer open-faq">
            <p class="font-primary font-bold text-2xl flex-1">Is Detect FaceShape free to use?</p>
            <PlusIcon />
          </div>
          <div class="h-0 overflow-hidden transition-all duration-300">
            <p class="text-[22px] pt-4">Yes, Detect FaceShape is free to use.</p>
          </div>
        </div>
        <div class="flex flex-col rounded-3xl bg-white p-8">
          <div class="flex justify-between items-center cursor-pointer open-faq">
            <p class="font-primary font-bold text-2xl flex-1">Can I use Detect FaceShape on mobile?</p>
            <PlusIcon />
          </div>
          <div class="h-0 overflow-hidden transition-all duration-300">
            <p class="text-[22px] pt-4">Yes, Detect FaceShape works on mobile, tablet, and desktop.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  <script>
    const faqElements = document.querySelectorAll('.open-faq');
    faqElements.forEach((element) => {
      element.addEventListener('click', () => {
        const answer = element.nextElementSibling as HTMLDivElement;
        if (answer.style.height) {
          answer.style.height = "";
          element.querySelector('svg')?.classList.remove('rotate-45');
        } else {
          answer.style.height = answer.scrollHeight + 'px';
          element.querySelector('svg')?.classList.add('rotate-45');
        }
      });
    });
  </script>
</Layout>
