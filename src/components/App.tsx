import React, { useState, useCallback } from "react";
import { type FaceShape, type FaceShapeResult } from "~/lib/types";
import { detectFaceShape } from "~/lib/services/geminiService";
import FileUpload from "./FileUpload";
import ResultDisplay from "./ResultDisplay";
import { FaceIcon, LoadingSpinnerIcon } from "./Icons";

const App: React.FC = () => {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [result, setResult] = useState<FaceShapeResult | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleImageSelect = useCallback((file: File) => {
    setImageFile(file);
    setPreviewUrl(URL.createObjectURL(file));
    setResult(null);
    setError(null);
  }, []);

  const handleDetect = useCallback(async () => {
    if (!imageFile) return;

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const reader = new FileReader();
      reader.readAsDataURL(imageFile);
      reader.onloadend = async () => {
        const base64String = (reader.result as string).split(",")[1];
        if (base64String) {
          const detectedResult = await detectFaceShape(
            base64String,
            imageFile.type
          );
          setResult(detectedResult);
        } else {
          throw new Error("Failed to convert image to Base64.");
        }
        setIsLoading(false);
      };
      reader.onerror = () => {
        throw new Error("Error reading the image file.");
      };
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred."
      );
      setIsLoading(false);
    }
  }, [imageFile]);

  const handleReset = () => {
    setImageFile(null);
    setPreviewUrl(null);
    setResult(null);
    setError(null);
    setIsLoading(false);
  };

  return (
    <div className="mt-9 w-3xl bg-primary/25 rounded-4xl p-6">
      {!previewUrl && <FileUpload onImageSelect={handleImageSelect} />}

      {previewUrl && (
        <div className="flex flex-col items-center w-full">
          <div className="w-full max-w-sm mb-6 rounded-lg overflow-hidden border-2 border-slate-600 shadow-lg">
            <img
              src={previewUrl}
              alt="Selected face"
              className="w-full h-auto object-cover"
            />
          </div>

          {!result && !isLoading && (
            <div className="flex items-center gap-4">
              <button
                onClick={handleDetect}
                disabled={isLoading}
                className="px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-slate-900 transition-all duration-200 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                Detect My Face Shape
              </button>
              <button
                onClick={handleReset}
                className="px-6 py-3 bg-slate-700 text-slate-300 font-semibold rounded-lg hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 focus:ring-offset-slate-900 transition-all duration-200 ease-in-out"
              >
                Try Another
              </button>
            </div>
          )}
        </div>
      )}

      {isLoading && (
        <div className="flex flex-col items-center justify-center text-center p-8">
          <LoadingSpinnerIcon className="w-12 h-12 mb-4" />
          <p className="text-lg font-semibold text-slate-300">
            Analyzing your features...
          </p>
          <p className="text-slate-400">This might take a moment.</p>
        </div>
      )}

      {error && (
        <div className="mt-6 text-center bg-red-900/50 border border-red-700 text-red-300 p-4 rounded-lg">
          <p className="font-bold">An Error Occurred</p>
          <p className="text-sm">{error}</p>
        </div>
      )}

      {result && previewUrl && (
        <ResultDisplay result={result} onReset={handleReset} />
      )}
    </div>
  );
};

export default App;
