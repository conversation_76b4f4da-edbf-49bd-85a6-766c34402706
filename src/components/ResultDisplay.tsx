import React from 'react';
import { FaceShapeResult } from '../lib/types';

interface ResultDisplayProps {
  result: FaceShapeResult;
  onReset: () => void;
}

const MatchBar: React.FC<{ shape: string; score: number, isTop: boolean }> = ({ shape, score, isTop }) => {
    const percentage = Math.round(score * 100);
    const barRef = React.useRef<HTMLDivElement>(null);
    
    React.useEffect(() => {
        const timer = setTimeout(() => {
            if(barRef.current) {
                barRef.current.style.width = `${percentage}%`;
            }
        }, 100);
        return () => clearTimeout(timer);
    }, [percentage]);

    return (
        <div className="mb-3">
            <div className="flex justify-between mb-1 items-center">
                <span className={`text-base font-medium ${isTop ? 'text-indigo-300' : 'text-slate-300'}`}>{shape}</span>
                <span className={`text-sm font-medium ${isTop ? 'text-indigo-300' : 'text-slate-400'}`}>{percentage}%</span>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2.5">
                <div 
                    ref={barRef}
                    className={`${isTop ? 'bg-gradient-to-r from-indigo-500 to-cyan-400' : 'bg-slate-500'} h-2.5 rounded-full transition-all duration-1000 ease-out`}
                    style={{ width: `0%` }}
                ></div>
            </div>
        </div>
    );
};

const ResultDisplay: React.FC<ResultDisplayProps> = ({ result, onReset }) => {
  const sortedMatches = React.useMemo(() => 
    [...result.matches].sort((a, b) => b.score - a.score),
    [result.matches]
  );

  return (
    <div className="text-center w-full animate-fade-in">
      <h2 className="text-3xl font-bold text-slate-200 mb-6">Face Shape Analysis</h2>
      
      <div className="space-y-4 mb-6 text-left">
          {sortedMatches.map((match, index) => (
              <MatchBar 
                  key={match.shape} 
                  shape={match.shape} 
                  score={match.score}
                  isTop={index === 0}
              />
          ))}
      </div>

      <div className="text-left bg-slate-900/70 p-4 rounded-lg border border-slate-700">
        <h3 className="font-semibold text-slate-300 mb-2">AI Reasoning:</h3>
        <p className="text-slate-400 text-sm">{result.reasoning}</p>
      </div>

      <button
        onClick={onReset}
        className="mt-8 px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-slate-900 transition-all duration-200 ease-in-out"
      >
        Analyze Another Photo
      </button>

      <style>{`
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
          animation: fade-in 0.5s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

export default ResultDisplay;
