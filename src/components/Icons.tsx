import React from "react";

export const UploadIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {
  const { className, ...rest } = props;

  return (
    <svg
      className={`w-15 h-15 ${className || ''}`}
      viewBox="0 0 68 68"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g>
        <path
          d="M65.1469 37.7644V20.8234C65.1469 15.8311 63.3488 11.0433 60.1482 7.51324C56.9476 3.98317 52.6066 2 48.0803 2H19.0671C14.5407 2 10.1998 3.98317 6.99918 7.51324C3.79857 11.0433 2.00049 15.8311 2.00049 20.8234V47.1761C2.00049 49.648 2.44193 52.0957 3.2996 54.3795C4.15728 56.6632 5.4144 58.7383 6.99918 60.4862C10.1998 64.0163 14.5407 65.9995 19.0671 65.9995H40.4344"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M2.88696 52.8209L12.2395 40.774C13.4677 39.4285 15.0797 38.5924 16.8026 38.4072C18.5256 38.222 20.2536 38.699 21.6943 39.7575C23.135 40.816 24.8631 41.2931 26.586 41.1078C28.309 40.9226 29.921 40.0865 31.1492 38.7411L39.1023 29.9694C41.3874 27.4404 44.413 25.8892 47.6391 25.5927C50.8651 25.2962 54.0818 26.2736 56.715 28.3505L65.1459 35.5411M19.9535 27.1082C20.6976 27.1033 21.4335 26.9367 22.1193 26.6181C22.805 26.2995 23.4271 25.835 23.9501 25.2512C24.4731 24.6674 24.8867 23.9757 25.1673 23.2156C25.4479 22.4555 25.59 21.6419 25.5855 20.8212C25.581 20.0005 25.43 19.1889 25.1412 18.4325C24.8523 17.6762 24.4311 16.9901 23.9018 16.4133C23.3725 15.8365 22.7454 15.3803 22.0562 15.0708C21.367 14.7613 20.6294 14.6045 19.8853 14.6095C18.3825 14.6195 16.9449 15.2875 15.8887 16.4665C14.8325 17.6455 14.2443 19.2391 14.2533 20.8965C14.2624 22.5539 14.868 24.1395 15.937 25.3044C17.006 26.4694 18.4508 27.1182 19.9535 27.1082Z"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M57.4459 47.1738V65.9972M48.9331 56.6043H65.9997"
          stroke="currentColor"
          strokeWidth="4"
          strokeMiterlimit="10"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};

export const FaceIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M12 2a10 10 0 0 0-10 10c0 4.42 3.58 8 8 8a10 10 0 0 0 10-10c0-4.42-3.58-8-8-8Z" />
    <circle cx="9" cy="10" r=".5" fill="currentColor" />
    <circle cx="15" cy="10" r=".5" fill="currentColor" />
    <path d="M8 14s1.5 2 4 2 4-2 4-2" />
  </svg>
);

export const LoadingSpinnerIcon: React.FC<React.SVGProps<SVGSVGElement>> = (
  props
) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="animate-spin text-indigo-400"
    {...props}
  >
    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
  </svg>
);
