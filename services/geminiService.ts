import { GoogleGenAI, Type } from "@google/genai";
import { FaceShape, FaceShapeResult } from '../types';

const ai = new GoogleGenAI({ apiKey: 'AIzaSyBnRk0BnoyrQWbUn7nhBQOYBwt_PTG0t_Y' as string });

const responseSchema = {
    type: Type.OBJECT,
    properties: {
        matches: {
            type: Type.ARRAY,
            description: "An array of objects, where each object represents a face shape and its corresponding match score.",
            items: {
                type: Type.OBJECT,
                properties: {
                    shape: {
                        type: Type.STRING,
                        enum: Object.values(FaceShape),
                        description: "The face shape."
                    },
                    score: {
                        type: Type.NUMBER,
                        description: "A confidence score between 0 and 1 on the prediction."
                    }
                },
                required: ['shape', 'score']
            }
        },
        reasoning: {
            type: Type.STRING,
            description: "A brief explanation of why this face shape was chosen, based on facial landmarks like forehead, cheekbones, and jawline."
        }
    },
    required: ['matches', 'reasoning']
};

export async function detectFaceShape(base64Image: string, mimeType: string): Promise<FaceShapeResult> {
    try {
        const response = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: {
                parts: [
                    {
                        inlineData: {
                            data: base64Image,
                            mimeType: mimeType
                        }
                    },
                    {
                        text: `Analyze the face in this image. For each of the following face shapes, provide a score from 0 to 1 indicating how well the face matches that shape: ${Object.values(FaceShape).filter(s => s !== FaceShape.UNKNOWN).join(', ')}. Also, provide a brief overall reasoning for your analysis based on facial landmarks like forehead, cheekbones, and jawline. Ensure your output strictly follows the provided JSON schema.`
                    }
                ]
            },
            config: {
                responseMimeType: 'application/json',
                responseSchema: responseSchema,
            }
        });

        const text = response.text.trim();
        const resultJson = JSON.parse(text);

        if (!resultJson.matches || !Array.isArray(resultJson.matches)) {
            throw new Error("API returned an invalid format for matches.");
        }

        const filteredMatches = resultJson.matches.filter((match: any) => 
            Object.values(FaceShape).includes(match.shape)
        );

        if(filteredMatches.length === 0 && resultJson.matches.length > 0) {
             console.warn("Model returned only unexpected shapes.", resultJson.matches);
             throw new Error("The AI model returned an unexpected analysis. Please try a different photo.");
        }

        return {
            matches: filteredMatches,
            reasoning: resultJson.reasoning || "No reasoning provided.",
        };

    } catch (error) {
        console.error("Error calling Gemini API:", error);
        if (error instanceof Error && error.message.includes('SAFETY')) {
             throw new Error("The request was blocked due to safety settings. Please use a different image.");
        }
        throw new Error("Could not analyze the image. Please ensure it's a clear photo of a face.");
    }
}
